{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-tabs": "^1.1.12", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@types/lodash": "^4.17.18", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "idb": "^8.0.3", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.511.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "rxjs": "^7.8.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.3", "typescript": "^5"}}