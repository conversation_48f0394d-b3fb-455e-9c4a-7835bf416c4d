'use client';

import { useState, useEffect } from 'react';
import { useSyncState } from '@/hooks/useSyncEvents';
import { getCapturesByFrameId } from '@/lib/db/captureOperations';
import { Capture } from '@/lib/db/types';

interface SyncStatusDebugProps {
  frameId: string;
}

export function SyncStatusDebug({ frameId }: SyncStatusDebugProps) {
  const [captures, setCaptures] = useState<Capture[]>([]);
  const [syncEvents, setSyncEvents] = useState<Array<{
    timestamp: number;
    event: string;
    data: any;
  }>>([]);

  // Monitor sync state changes
  useSyncState((syncState) => {
    setSyncEvents(prev => [...prev.slice(-9), {
      timestamp: Date.now(),
      event: 'sync-state-change',
      data: {
        isProcessing: syncState.isProcessing,
        stats: syncState.stats,
        progress: syncState.progress
      }
    }]);
  });

  // Load captures periodically to see updates
  useEffect(() => {
    const loadCaptures = async () => {
      try {
        const frameCaptures = await getCapturesByFrameId(frameId);
        setCaptures(frameCaptures);
      } catch (error) {
        console.error('Error loading captures for debug:', error);
      }
    };

    loadCaptures();
    const interval = setInterval(loadCaptures, 2000);
    return () => clearInterval(interval);
  }, [frameId]);

  const captureStats = {
    synced: captures.filter(c => c.syncStatus === 'synced').length,
    pending: captures.filter(c => c.syncStatus === 'pending').length,
    failed: captures.filter(c => c.syncStatus === 'conflict').length,
    total: captures.length
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="text-sm font-semibold mb-2">Sync Debug - Frame: {frameId.substring(0, 8)}...</h3>
      
      <div className="text-xs space-y-1 mb-3">
        <div>Total Captures: {captureStats.total}</div>
        <div className="text-green-600">✅ Synced: {captureStats.synced}</div>
        <div className="text-yellow-600">⏳ Pending: {captureStats.pending}</div>
        <div className="text-red-600">❌ Failed: {captureStats.failed}</div>
      </div>

      <div className="text-xs">
        <div className="font-semibold mb-1">Recent Sync Events:</div>
        <div className="max-h-32 overflow-y-auto space-y-1">
          {syncEvents.slice(-5).map((event, index) => (
            <div key={index} className="text-xs">
              <div className="text-gray-500">
                {new Date(event.timestamp).toLocaleTimeString()}
              </div>
              <div>
                Processing: {event.data.isProcessing ? 'Yes' : 'No'}
              </div>
              <div>
                Pending: {event.data.stats.pending}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
