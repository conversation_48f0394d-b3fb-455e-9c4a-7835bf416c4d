'use client';

import { useState, useEffect, useCallback } from 'react';
import { sessionSyncService } from '@/lib/services/sessionSync';
import { SyncStatusCompact } from '@/components/sync/SyncStatus';
import { useEnhancedSyncState } from '@/hooks/useEnhancedSync';

export default function ConnectionStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [serverConnected, setServerConnected] = useState(false);
  const [checking, setChecking] = useState(false);

  // Stable server connection check function
  const checkServerConnection = useCallback(async () => {
    if (!navigator.onLine) return;

    setChecking(true);
    try {
      const connected = await sessionSyncService.checkServerConnectivity();
      setServerConnected(connected);
    } catch {
      setServerConnected(false);
    } finally {
      setChecking(false);
    }
  }, []);

  // Reactive sync state monitoring for connection updates
  useEnhancedSyncState(useCallback((syncState: any) => {
    // When sync operations complete successfully, it indicates server connectivity
    if (!syncState.isProcessing && syncState.stats.completed > 0) {
      console.log('[ConnectionStatus] Sync completed successfully - server is connected');
      setServerConnected(true);
    }

    // If sync operations fail consistently, it might indicate connectivity issues
    if (!syncState.isProcessing && syncState.stats.failed > 0 && syncState.stats.completed === 0) {
      console.log('[ConnectionStatus] Sync operations failed - checking server connectivity');
      // Re-check server connection when sync fails
      if (isOnline) {
        checkServerConnection();
      }
    }
  }, [isOnline, checkServerConnection]));

  useEffect(() => {
    // Check initial online status
    setIsOnline(navigator.onLine);

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      checkServerConnection();
    };

    const handleOffline = () => {
      setIsOnline(false);
      setServerConnected(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial server check
    if (navigator.onLine) {
      checkServerConnection();
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [checkServerConnection]); // Added missing dependency

  const getStatusColor = () => {
    if (!isOnline) return 'bg-gray-500';
    if (checking) return 'bg-yellow-500';
    if (serverConnected) return 'bg-green-500';
    return 'bg-orange-500';
  };

  const getStatusText = () => {
    if (!isOnline) return 'Offline';
    if (checking) return 'Checking...';
    if (serverConnected) return 'Connected';
    return 'Local Only';
  };

  const getStatusDescription = () => {
    if (!isOnline) return 'No internet connection';
    if (checking) return 'Checking server connection...';
    if (serverConnected) return 'Connected to backend server';
    return 'Working offline - data stored locally';
  };

  return (
    <div className="flex items-center space-x-4">
      {/* Connection Status */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()} ${checking ? 'animate-pulse' : ''}`}></div>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {getStatusText()}
          </span>
        </div>
        
        <div className="hidden sm:block">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {getStatusDescription()}
          </span>
        </div>

        {!isOnline && (
          <button
            onClick={() => window.location.reload()}
            className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
          >
            Retry
          </button>
        )}

        {isOnline && !serverConnected && !checking && (
          <button
            onClick={checkServerConnection}
            className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
          >
            Retry Server
          </button>
        )}
      </div>

      {/* Sync Status - only show when connected */}
      {serverConnected && (
        <div className="border-l border-gray-300 dark:border-gray-600 pl-3">
          <SyncStatusCompact />
        </div>
      )}
    </div>
  );
}