'use client';

import { useState, useEffect, useCallback } from 'react';
import { Frame, Capture } from '@/lib/db/types';
import { deleteFrame } from '@/lib/db/frameOperations';
import { getCapturesByFrameId } from '@/lib/db/captureOperations';
import { FrameSyncStatusDetailed } from '@/components/sync/FrameSyncStatus';
import { useReactiveCaptures, useFrameSyncState } from '@/hooks/useEnhancedSync';

interface SessionCardProps {
  session: Frame;
  onClick: () => void;
  onDelete?: (frameId: string) => void;
}

export default function SessionCard({ session, onClick, onDelete }: SessionCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [captures, setCaptures] = useState<Capture[]>([]);
  const [syncing, setSyncing] = useState(false);

  // Stable function to load captures
  const loadCaptures = useCallback(async () => {
    try {
      console.log('[SessionCard] Loading captures for frame:', session.frameId);
      const frameCaptures = await getCapturesByFrameId(session.frameId);
      console.log('[SessionCard] Loaded captures:', {
        frameId: session.frameId,
        count: frameCaptures.length,
        syncStatuses: frameCaptures.map(c => ({ id: c.captureId.substring(0, 8), status: c.syncStatus }))
      });
      setCaptures(frameCaptures);
    } catch (error) {
      console.error('Error loading captures for session card:', error);
    }
  }, [session.frameId]);

  // Load captures for this frame on mount and when frameId changes
  useEffect(() => {
    loadCaptures();
  }, [loadCaptures]);

  // Use reactive capture loading - automatically reloads when database changes
  useReactiveCaptures(session.frameId, loadCaptures);

  // Use frame-specific sync state for immediate updates
  const { frameState } = useFrameSyncState(session.frameId);

  // Update syncing state based on frame sync state
  useEffect(() => {
    if (frameState) {
      // Consider frame as syncing if it has pending items
      setSyncing(frameState.pending > 0);
      console.log('[SessionCard] Frame sync state updated:', {
        frameId: session.frameId,
        frameState,
        syncing: frameState.pending > 0
      });
    }
  }, [frameState, session.frameId]);

  // No longer needed - reactive system handles sync completion automatically

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };


  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!showDeleteConfirm) {
      setShowDeleteConfirm(true);
      return;
    }

    setIsDeleting(true);
    try {
      await deleteFrame(session.frameId);
      onDelete?.(session.frameId);
    } catch (error) {
      console.error('Error deleting session:', error);
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleCancelDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteConfirm(false);
  };

  return (
    <div
      onClick={onClick}
      className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer group"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 dark:text-white text-sm truncate">
            {session.modelNumber}
          </h3>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {formatDate(session.creationTimestamp)}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {/* Delete Button */}
          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
            {showDeleteConfirm ? (
              <div className="flex space-x-1">
                <button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="p-1 text-red-600 hover:text-red-700 disabled:opacity-50"
                  title="Confirm delete"
                >
                  {isDeleting ? (
                    <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </button>
                <button
                  onClick={handleCancelDelete}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  title="Cancel delete"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ) : (
              <button
                onClick={handleDelete}
                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                title="Delete session"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Status Badge and Sync Status */}
      <div className="mb-3 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
            {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
          </span>
          {syncing && (
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-xs text-blue-600">Syncing...</span>
            </div>
          )}
        </div>
        <div className="flex items-center origin-right" onClick={(e) => e.stopPropagation()}>
          <FrameSyncStatusDetailed
            key={`sync-${session.frameId}-${captures.length}`}
            frameId={session.frameId}
            captures={captures}
          />
        </div>
      </div>

      {/* Details */}
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-500 dark:text-gray-400">Machine:</span>
          <span className="text-gray-900 dark:text-white font-mono text-xs truncate ml-2">
            {session.machineSerialNumber}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500 dark:text-gray-400">Inspector:</span>
          <span className="text-gray-900 dark:text-white truncate ml-2">
            {session.inspectorName}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500 dark:text-gray-400">Captures:</span>
          <span className="text-gray-900 dark:text-white font-semibold">
            {captures.length}
          </span>
        </div>
      </div>

      {/* Last Modified */}
      {session.lastModifiedTimestamp !== session.creationTimestamp && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Modified: {formatDate(session.lastModifiedTimestamp)}
          </p>
        </div>
      )}

      {/* Action Hint */}
      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            Click to continue
          </span>
          <svg className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </div>
  );
}