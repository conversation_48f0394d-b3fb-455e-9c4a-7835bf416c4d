'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { Loader2, <PERSON><PERSON>ircle, XCircle, Clock, RefreshCw, AlertCircle } from 'lucide-react';
// Reactive sync operations
import { useFrameSyncState, useEnhancedSyncState, useReactiveSyncOperations } from '@/hooks/useEnhancedSync';
import { Capture } from '@/lib/db/types';

interface FrameSyncStatusProps {
  frameId: string;
  captures: Capture[];
  className?: string;
  showDetails?: boolean;
}

export function FrameSyncStatus({
  frameId,
  captures,
  className = '',
  showDetails = true
}: FrameSyncStatusProps) {
  const [error, setError] = useState<string | null>(null);

  // Reactive sync operations
  const { triggerFrameSync, stopSync, isOperating } = useReactiveSyncOperations();

  // Calculate frame-specific sync stats from captures (reactive to captures prop)
  const captureStats = useMemo(() => {
    const stats = {
      synced: captures.filter(c => c.syncStatus === 'synced').length,
      pending: captures.filter(c => c.syncStatus === 'pending').length,
      failed: captures.filter(c => c.syncStatus === 'conflict').length,
      total: captures.length
    };

    console.log('[FrameSyncStatus] Calculated capture stats for frame:', frameId, {
      stats,
      captureDetails: captures.map(c => ({
        id: c.captureId.substring(0, 8),
        status: c.syncStatus,
        timestamp: c.captureTimestamp
      }))
    });

    return stats;
  }, [captures, frameId]);

  // Get reactive frame sync state for real-time updates
  const { frameState } = useFrameSyncState(frameId);

  // Get global sync state for progress tracking
  const [globalSyncState, setGlobalSyncState] = useState<any>(null);

  useEnhancedSyncState(useCallback((state: any) => {
    console.log('[FrameSyncStatus] Global sync state updated:', {
      frameId,
      isProcessing: state.isProcessing,
      progress: state.progress,
      stats: state.stats
    });
    setGlobalSyncState(state);
  }, [frameId]));

  // Determine if this frame is currently syncing
  const isFrameSyncing = useMemo(() => {
    // Check if global sync is processing and has progress for this frame
    const hasFrameProgress = globalSyncState?.progress?.isProcessing &&
                            globalSyncState.progress.currentItem?.includes(frameId);

    // Or check if frame state indicates pending items being processed
    const hasFramePending = frameState && frameState.pending > 0;

    return hasFrameProgress || hasFramePending || false;
  }, [globalSyncState, frameState, frameId]);

  // Get current progress for this frame
  const frameProgress = useMemo(() => {
    console.log('[FrameSyncStatus] Computing frame progress:', {
      frameId,
      hasGlobalState: !!globalSyncState,
      isProcessing: globalSyncState?.progress?.isProcessing,
      currentItem: globalSyncState?.progress?.currentItem,
      progress: globalSyncState?.progress
    });

    if (!globalSyncState?.progress?.isProcessing) {
      console.log('[FrameSyncStatus] No processing progress for frame:', frameId);
      return null;
    }

    // Show progress if it's related to this frame OR if it's a frame sync operation
    const isFrameRelated = globalSyncState.progress.currentItem?.includes(frameId) ||
                          globalSyncState.progress.currentItem?.includes('frame');

    if (isFrameRelated) {
      console.log('[FrameSyncStatus] Frame progress detected:', {
        frameId,
        progress: globalSyncState.progress
      });
      return globalSyncState.progress;
    }

    console.log('[FrameSyncStatus] Progress not related to frame:', frameId);
    return null;
  }, [globalSyncState, frameId]);

  const handleFrameSync = useCallback(async () => {
    if (isFrameSyncing || isOperating) {
      // Stop current sync
      console.log('[FrameSyncStatus] Stopping frame sync for:', frameId);
      await stopSync();
      return;
    }

    try {
      setError(null);
      console.log('[FrameSyncStatus] Starting reactive frame sync for:', frameId);

      // Trigger frame sync through reactive system - progress updates will come automatically
      await triggerFrameSync(frameId);

      console.log('[FrameSyncStatus] Reactive frame sync completed for:', frameId);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Frame sync failed';
      console.error('[FrameSyncStatus] Reactive frame sync failed:', errorMessage);
      setError(errorMessage);
    }
  }, [frameId, isFrameSyncing, isOperating, triggerFrameSync, stopSync]);

  const getSyncStatusIcon = () => {
    if (isFrameSyncing) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
    }

    // Use capture stats for more accurate representation
    if (captureStats.failed > 0) {
      return <AlertCircle className="w-4 h-4 text-red-600" />;
    }

    if (captureStats.pending > 0) {
      return <Clock className="w-4 h-4 text-yellow-600" />;
    }

    if (captureStats.total === 0) {
      return <Clock className="w-4 h-4 text-gray-400" />;
    }

    return <CheckCircle className="w-4 h-4 text-green-600" />;
  };

  const getSyncStatusText = () => {
    if (isFrameSyncing) {
      return 'Syncing frame...';
    }

    if (captureStats.failed > 0) {
      return `${captureStats.failed} failed`;
    }

    if (captureStats.pending > 0) {
      return `${captureStats.pending} pending`;
    }

    if (captureStats.total === 0) {
      return 'No captures';
    }

    return 'Frame synced';
  };

  const getProgressPercentage = () => {
    if (!frameProgress || frameProgress.totalItems === 0) return 0;
    return Math.round((frameProgress.processedItems / frameProgress.totalItems) * 100);
  };

  const needsSync = captureStats.pending > 0 || captureStats.failed > 0;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Sync Status Icon and Text */}
      <div className="flex items-center space-x-1">
        {getSyncStatusIcon()}
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {getSyncStatusText()}
        </span>
      </div>

      {/* Manual Sync Button - only show if there are items to sync */}
      {needsSync && (
        <button
          onClick={handleFrameSync}
          disabled={!navigator.onLine}
          className={`p-1 rounded-md transition-colors ${
            isFrameSyncing
              ? 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'
              : 'text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20'
          } ${!navigator.onLine ? 'opacity-50 cursor-not-allowed' : ''}`}
          title={isFrameSyncing ? 'Stop frame sync' : `Sync ${captureStats.pending + captureStats.failed} items from this frame`}
        >
          {isFrameSyncing ? (
            <XCircle className="w-4 h-4" />
          ) : (
            <RefreshCw className="w-4 h-4" />
          )}
        </button>
      )}

      {/* Progress Details (when syncing) */}
      {frameProgress && isFrameSyncing && showDetails && (
        <div className="flex flex-col space-y-1 min-w-0">
          <div className="flex items-center space-x-2">
            <div className="w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
              <div
                className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage()}%` }}
              ></div>
            </div>
            <span className="text-xs text-gray-500 whitespace-nowrap">
              {frameProgress.processedItems}/{frameProgress.totalItems}
            </span>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="text-xs text-red-600 dark:text-red-400 max-w-xs truncate" title={error}>
          {error}
        </div>
      )}

      {/* Detailed Stats (when expanded) */}
      {showDetails && !frameProgress && captureStats.total > 0 && (
        <div className="text-xs text-gray-500 space-x-2">
          {captureStats.pending > 0 && <span>⏳ {captureStats.pending}</span>}
          {captureStats.failed > 0 && <span>❌ {captureStats.failed}</span>}
          {captureStats.synced > 0 && <span>✅ {captureStats.synced}</span>}
        </div>
      )}
    </div>
  );
}

// Compact version for headers/toolbars
export function FrameSyncStatusCompact({
  frameId,
  captures,
  className = ''
}: Omit<FrameSyncStatusProps, 'showDetails'>) {
  return (
    <FrameSyncStatus
      frameId={frameId}
      captures={captures}
      showDetails={false}
      className={className}
    />
  );
}

// Detailed version for panels/modals
export function FrameSyncStatusDetailed({
  frameId,
  captures,
  className = ''
}: Omit<FrameSyncStatusProps, 'showDetails'>) {
  return (
    <FrameSyncStatus
      frameId={frameId}
      captures={captures}
      showDetails={true}
      className={className}
    />
  );
}