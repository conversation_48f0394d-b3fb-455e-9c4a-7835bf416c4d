'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Loader2, CheckCircle, XCircle, Clock, RefreshCw, AlertCircle } from 'lucide-react';
import { startFrameSync, getFrameSyncStatus, stopSync } from '@/lib/sync/syncManager';
import { useSyncState, useSyncProgress } from '@/hooks/useSyncEvents';
import { Capture } from '@/lib/db/types';

interface FrameSyncStatusProps {
  frameId: string;
  captures: Capture[];
  className?: string;
  onSyncComplete?: () => void;
  showDetails?: boolean;
}

interface FrameSyncStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}

interface SyncProgress {
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  currentItem?: string;
  isProcessing: boolean;
}

export function FrameSyncStatus({ 
  frameId, 
  captures, 
  className = '', 
  onSyncComplete,
  showDetails = true
}: FrameSyncStatusProps) {
  const [, setSyncStats] = useState<FrameSyncStats>({ pending: 0, processing: 0, completed: 0, failed: 0 });
  const [progress, setProgress] = useState<SyncProgress | null>(null);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate frame-specific sync stats from captures
  const captureStats = useMemo(() => ({
    synced: captures.filter(c => c.syncStatus === 'synced').length,
    pending: captures.filter(c => c.syncStatus === 'pending').length,
    failed: captures.filter(c => c.syncStatus === 'conflict').length,
    total: captures.length
  }), [captures]);

  // Enhanced sync state monitoring for frame-specific updates
  const lastProcessingState = useRef(false);
  const lastCaptureCount = useRef(captures.length);

  // Monitor global sync state for frame-specific changes
  useSyncState((syncState) => {
    console.log('[FrameSyncStatus] Sync state update:', {
      frameId,
      isProcessing: syncState.isProcessing,
      lastProcessingState: lastProcessingState.current,
      stats: syncState.stats,
      progress: syncState.progress,
      captureCount: captures.length
    });

    // Update syncing state from global sync manager
    setSyncing(syncState.isProcessing);

    // Clear progress when sync completes to prevent stale progress bar
    if (!syncState.isProcessing) {
      setProgress(null);
    }

    // When sync completes, trigger parent to reload captures
    if (lastProcessingState.current && !syncState.isProcessing) {
      console.log('[FrameSyncStatus] Sync completed, triggering onSyncComplete for frame:', frameId);
      if (onSyncComplete) {
        // Add a small delay to ensure sync state has propagated
        setTimeout(() => {
          onSyncComplete();
        }, 50);
      }
    }
    lastProcessingState.current = syncState.isProcessing;
  });

  // Force re-render when captures are updated after sync
  useEffect(() => {
    if (lastCaptureCount.current !== captures.length) {
      console.log('[FrameSyncStatus] Captures updated for frame:', frameId, {
        oldCount: lastCaptureCount.current,
        newCount: captures.length
      });
      lastCaptureCount.current = captures.length;
      // Force a state update to trigger re-render of sync status
      // Use a functional update to avoid potential infinite loops
      setSyncing(current => current);
    }
  }, [captures.length, frameId]);

  // Monitor sync progress for real-time updates
  useSyncProgress((progressUpdate) => {
    if (progressUpdate && progressUpdate.isProcessing) {
      // Only set progress when actively processing
      setProgress(progressUpdate);
    } else {
      // Clear progress when not processing or progress is null
      setProgress(null);
    }
    // Don't update syncing state here - let useSyncState handle it to avoid conflicts
  });

  // Update sync stats when frameId changes
  useEffect(() => {
    const updateFrameStats = async () => {
      try {
        const frameStats = await getFrameSyncStatus(frameId);
        setSyncStats(frameStats);
      } catch (err) {
        console.error('Failed to get frame sync stats:', err);
      }
    };

    updateFrameStats();
  }, [frameId]); // Only update when frameId changes

  const handleFrameSync = useCallback(async () => {
    if (syncing) {
      // Stop current sync
      stopSync();
      setSyncing(false);
      setProgress(null);
      return;
    }

    try {
      setError(null);
      setSyncing(true);

      // Enhanced: Progress updates handled by useSyncProgress hook
      await startFrameSync(frameId);

      // Parent component will be notified via useSyncState hook when sync completes

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Frame sync failed');
      // Reset state on error
      setSyncing(false);
      setProgress(null);
    }
    // Note: Don't use finally block - let useSyncProgress handle state cleanup
  }, [frameId, syncing]);

  const getSyncStatusIcon = () => {
    if (syncing) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
    }

    // Use capture stats for more accurate representation
    if (captureStats.failed > 0) {
      return <AlertCircle className="w-4 h-4 text-red-600" />;
    }

    if (captureStats.pending > 0) {
      return <Clock className="w-4 h-4 text-yellow-600" />;
    }

    if (captureStats.total === 0) {
      return <Clock className="w-4 h-4 text-gray-400" />;
    }

    return <CheckCircle className="w-4 h-4 text-green-600" />;
  };

  const getSyncStatusText = () => {
    if (syncing) {
      return 'Syncing frame...';
    }

    if (captureStats.failed > 0) {
      return `${captureStats.failed} failed`;
    }

    if (captureStats.pending > 0) {
      return `${captureStats.pending} pending`;
    }

    if (captureStats.total === 0) {
      return 'No captures';
    }

    return 'Frame synced';
  };

  const getProgressPercentage = () => {
    if (!progress || progress.totalItems === 0) return 0;
    return Math.round((progress.processedItems / progress.totalItems) * 100);
  };

  const needsSync = captureStats.pending > 0 || captureStats.failed > 0;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Sync Status Icon and Text */}
      <div className="flex items-center space-x-1">
        {getSyncStatusIcon()}
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {getSyncStatusText()}
        </span>
      </div>

      {/* Manual Sync Button - only show if there are items to sync */}
      {needsSync && (
        <button
          onClick={handleFrameSync}
          disabled={!navigator.onLine}
          className={`p-1 rounded-md transition-colors ${
            syncing
              ? 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'
              : 'text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20'
          } ${!navigator.onLine ? 'opacity-50 cursor-not-allowed' : ''}`}
          title={syncing ? 'Stop frame sync' : `Sync ${captureStats.pending + captureStats.failed} items from this frame`}
        >
          {syncing ? (
            <XCircle className="w-4 h-4" />
          ) : (
            <RefreshCw className="w-4 h-4" />
          )}
        </button>
      )}

      {/* Progress Details (when syncing) */}
      {progress && syncing && showDetails && (
        <div className="flex flex-col space-y-1 min-w-0">
          <div className="flex items-center space-x-2">
            <div className="w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
              <div 
                className="bg-blue-600 h-1.5 rounded-full transition-all duration-300" 
                style={{ width: `${getProgressPercentage()}%` }}
              ></div>
            </div>
            <span className="text-xs text-gray-500 whitespace-nowrap">
              {progress.processedItems}/{progress.totalItems}
            </span>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="text-xs text-red-600 dark:text-red-400 max-w-xs truncate" title={error}>
          {error}
        </div>
      )}

      {/* Detailed Stats (when expanded) */}
      {showDetails && !progress && captureStats.total > 0 && (
        <div className="text-xs text-gray-500 space-x-2">
          {captureStats.pending > 0 && <span>⏳ {captureStats.pending}</span>}
          {captureStats.failed > 0 && <span>❌ {captureStats.failed}</span>}
          {captureStats.synced > 0 && <span>✅ {captureStats.synced}</span>}
        </div>
      )}
    </div>
  );
}

// Compact version for headers/toolbars
export function FrameSyncStatusCompact({ 
  frameId, 
  captures, 
  className = '',
  onSyncComplete 
}: Omit<FrameSyncStatusProps, 'showDetails'>) {
  return (
    <FrameSyncStatus 
      frameId={frameId}
      captures={captures}
      showDetails={false} 
      className={className}
      onSyncComplete={onSyncComplete}
    />
  );
}

// Detailed version for panels/modals
export function FrameSyncStatusDetailed({ 
  frameId, 
  captures, 
  className = '',
  onSyncComplete 
}: Omit<FrameSyncStatusProps, 'showDetails'>) {
  return (
    <FrameSyncStatus 
      frameId={frameId}
      captures={captures}
      showDetails={true} 
      className={className}
      onSyncComplete={onSyncComplete}
    />
  );
}