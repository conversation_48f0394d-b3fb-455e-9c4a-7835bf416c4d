'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { enhancedSyncStateManager } from '@/lib/sync/enhancedSyncStateManager';
import { reactiveDatabase } from '@/lib/db/reactiveDatabase';
import type { EnhancedSyncState, FrameState, ItemUpdate } from '@/lib/sync/enhancedSyncStateManager';
import type { DatabaseChange } from '@/lib/db/reactiveDatabase';

/**
 * Enhanced hook for reactive sync state management
 * 
 * Provides immediate updates when sync operations complete, eliminating the need for polling.
 */
export function useEnhancedSyncState(callback?: (state: EnhancedSyncState) => void) {
  const [syncState, setSyncState] = useState<EnhancedSyncState | null>(null);
  const callbackRef = useRef(callback);
  
  // Update callback ref when it changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    console.log('[useEnhancedSyncState] Setting up sync state subscription');
    
    const subscription = enhancedSyncStateManager.getSyncState().subscribe((state) => {
      console.log('[useEnhancedSyncState] Sync state updated:', {
        isProcessing: state.isProcessing,
        stats: state.stats,
        frameStatesCount: state.frameStates.size,
        itemUpdatesCount: state.itemUpdates.length
      });
      
      setSyncState(state);
      
      if (callbackRef.current) {
        callbackRef.current(state);
      }
    });

    return () => {
      console.log('[useEnhancedSyncState] Cleaning up sync state subscription');
      subscription.unsubscribe();
    };
  }, []);

  return syncState;
}

/**
 * Hook for frame-specific sync state
 * 
 * Provides reactive updates for a specific frame's sync status.
 */
export function useFrameSyncState(frameId: string) {
  const [frameState, setFrameState] = useState<FrameState | null>(null);
  const [itemUpdates, setItemUpdates] = useState<ItemUpdate[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!frameId) return;

    console.log('[useFrameSyncState] Setting up frame sync state for:', frameId);
    setIsLoading(true);

    // Subscribe to frame-specific state
    const frameStateSubscription = enhancedSyncStateManager
      .getFrameState(frameId)
      .subscribe((state) => {
        console.log('[useFrameSyncState] Frame state updated:', { frameId, state });
        setFrameState(state);
        setIsLoading(false);
      });

    // Subscribe to frame-specific item updates
    const itemUpdatesSubscription = enhancedSyncStateManager
      .getFrameItemUpdates(frameId)
      .subscribe((updates) => {
        console.log('[useFrameSyncState] Frame item updates:', { frameId, updatesCount: updates.length });
        setItemUpdates(updates);
      });

    return () => {
      console.log('[useFrameSyncState] Cleaning up frame sync state for:', frameId);
      frameStateSubscription.unsubscribe();
      itemUpdatesSubscription.unsubscribe();
    };
  }, [frameId]);

  return {
    frameState,
    itemUpdates,
    isLoading
  };
}

/**
 * Hook for database change notifications
 * 
 * Provides reactive updates when database items change.
 */
export function useDatabaseChanges(frameId?: string) {
  const [changes, setChanges] = useState<DatabaseChange[]>([]);
  const [lastChange, setLastChange] = useState<DatabaseChange | null>(null);

  useEffect(() => {
    console.log('[useDatabaseChanges] Setting up database change subscription for frame:', frameId);

    const subscription = frameId
      ? reactiveDatabase.getFrameChanges(frameId)
      : reactiveDatabase.getChanges();

    const changeSubscription = subscription.subscribe((dbChanges) => {
      console.log('[useDatabaseChanges] Database changes received:', {
        frameId,
        changesCount: dbChanges.length,
        latestChange: dbChanges[dbChanges.length - 1]
      });
      
      setChanges(dbChanges);
      
      if (dbChanges.length > 0) {
        setLastChange(dbChanges[dbChanges.length - 1]);
      }
    });

    return () => {
      console.log('[useDatabaseChanges] Cleaning up database change subscription');
      changeSubscription.unsubscribe();
    };
  }, [frameId]);

  return {
    changes,
    lastChange
  };
}

/**
 * Hook for sync completion detection
 * 
 * Provides callbacks when sync operations complete for specific frames or globally.
 */
export function useSyncCompletion(
  onSyncComplete?: () => void,
  frameId?: string
) {
  const onSyncCompleteRef = useRef(onSyncComplete);
  const lastProcessingState = useRef(false);

  // Update callback ref when it changes
  useEffect(() => {
    onSyncCompleteRef.current = onSyncComplete;
  }, [onSyncComplete]);

  useEffect(() => {
    if (!onSyncCompleteRef.current) return;

    console.log('[useSyncCompletion] Setting up sync completion detection for frame:', frameId);

    const subscription = enhancedSyncStateManager.getSyncState().subscribe((state) => {
      // Detect sync completion (processing -> not processing)
      if (lastProcessingState.current && !state.isProcessing) {
        console.log('[useSyncCompletion] Sync completion detected for frame:', frameId);
        
        if (onSyncCompleteRef.current) {
          // Add small delay to ensure database changes have propagated
          setTimeout(() => {
            onSyncCompleteRef.current?.();
          }, 50);
        }
      }
      
      lastProcessingState.current = state.isProcessing;
    });

    return () => {
      console.log('[useSyncCompletion] Cleaning up sync completion detection');
      subscription.unsubscribe();
    };
  }, [frameId]);
}

/**
 * Hook for reactive capture loading
 * 
 * Automatically reloads captures when database changes occur for a frame.
 */
export function useReactiveCaptures(
  frameId: string,
  loadCaptures: () => Promise<void>
) {
  const loadCapturesRef = useRef(loadCaptures);
  const [isLoading, setIsLoading] = useState(false);
  const [hasInitialLoad, setHasInitialLoad] = useState(false);

  // Update callback ref when it changes
  useEffect(() => {
    loadCapturesRef.current = loadCaptures;
  }, [loadCaptures]);

  // Initial load on mount
  useEffect(() => {
    if (!hasInitialLoad) {
      console.log('[useReactiveCaptures] Initial load for frame:', frameId);
      setIsLoading(true);
      setHasInitialLoad(true);

      loadCapturesRef.current().finally(() => {
        setIsLoading(false);
      });
    }
  }, [frameId, hasInitialLoad]);

  // Subscribe to database changes for this frame
  const { lastChange } = useDatabaseChanges(frameId);

  // Reload captures when database changes occur (but not on initial load)
  useEffect(() => {
    if (!lastChange || lastChange.frameId !== frameId || !hasInitialLoad) return;

    console.log('[useReactiveCaptures] Database change detected, reloading captures:', {
      frameId,
      changeType: lastChange.type,
      changeId: lastChange.id
    });

    setIsLoading(true);

    // Small delay to ensure database transaction is committed
    setTimeout(async () => {
      try {
        await loadCapturesRef.current();
      } catch (error) {
        console.error('[useReactiveCaptures] Error reloading captures:', error);
      } finally {
        setIsLoading(false);
      }
    }, 100);
  }, [lastChange, frameId, hasInitialLoad]);

  return {
    isLoading
  };
}

/**
 * Legacy compatibility hook
 * 
 * Provides backward compatibility with existing useSyncState usage.
 */
export function useSyncState(callback: (state: any) => void) {
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEnhancedSyncState(useCallback((state) => {
    // Convert enhanced state to legacy format for compatibility
    const legacyState = {
      isProcessing: state.isProcessing,
      stats: state.stats,
      progress: state.progress
    };
    
    callbackRef.current(legacyState);
  }, []));
}
