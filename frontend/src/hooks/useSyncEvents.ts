'use client';

import { useEffect, useCallback } from 'react';
import { syncEventEmitter } from '@/lib/sync/syncManager';
import { syncStateManager } from '@/lib/sync/syncStateManager';
import type { SyncState } from '@/lib/sync/syncStateManager';

/**
 * Custom hook to listen for sync completion events
 * MIGRATION: This hook now uses both legacy and new event systems for compatibility
 * @param callback Function to call when sync completes
 */
export function useSyncEvents(callback: () => void) {
  const stableCallback = useCallback(callback, [callback]);

  useEffect(() => {
    // Legacy event system (for backward compatibility)
    syncEventEmitter.addEventListener(stableCallback);

    // New reactive state system (primary)
    let lastProcessingState = false;
    const subscription = syncStateManager.getSyncState().subscribe((syncState) => {
      // Trigger callback when sync processing completes (true -> false)
      if (lastProcessingState && !syncState.isProcessing) {
        stableCallback();
      }
      lastProcessingState = syncState.isProcessing;
    });

    return () => {
      syncEventEmitter.removeEventListener(stableCallback);
      subscription.unsubscribe();
    };
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to reactive sync state updates
 * @param callback Function to call when sync state changes
 */
export function useSyncState(callback: (state: SyncState) => void) {
  const stableCallback = useCallback(callback, [callback]);

  useEffect(() => {
    const subscription = syncStateManager.getSyncState().subscribe(stableCallback);

    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to sync stats only
 * @param callback Function to call when sync stats change
 */
export function useSyncStats(callback: (stats: { pending: number; processing: number; completed: number; failed: number; }) => void) {
  const stableCallback = useCallback(callback, [callback]);

  useEffect(() => {
    const subscription = syncStateManager.getSyncStats().subscribe(stableCallback);

    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to sync progress updates
 * @param callback Function to call when sync progress changes
 */
export function useSyncProgress(callback: (progress: { totalItems: number; processedItems: number; successfulItems: number; failedItems: number; currentItem?: string; isProcessing: boolean; } | null) => void) {
  const stableCallback = useCallback(callback, [callback]);

  useEffect(() => {
    const subscription = enhancedSyncStateManager.getSyncState().subscribe((enhancedState) => {
      stableCallback(enhancedState.progress);
    });

    return () => subscription.unsubscribe();
  }, [stableCallback]);
}