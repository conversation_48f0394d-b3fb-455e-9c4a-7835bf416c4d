// src/lib/db/captureOperations.ts
import { Capture, DetectionResult } from './types';
import { getDB } from './index';
import { incrementCaptureCount } from './frameOperations';
import { addCaptureToSyncQueue } from './syncQueueHelper';
import { reactiveDatabase } from './reactiveDatabase';

// Create a thumbnail from a larger image blob
async function createThumbnail(imageBlob: Blob, maxSize: number = 100): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      // Calculate thumbnail dimensions
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Could not create canvas context'));
        return;
      }
      
      const aspectRatio = img.width / img.height;
      let width = maxSize;
      let height = maxSize;
      
      if (aspectRatio > 1) {
        height = width / aspectRatio;
      } else {
        width = height * aspectRatio;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw and resize the image
      ctx.drawImage(img, 0, 0, width, height);
      
      // Convert back to blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create thumbnail blob'));
          }
        },
        'image/jpeg',
        0.7 // Quality
      );
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image for thumbnail generation'));
    };
    
    img.src = URL.createObjectURL(imageBlob);
  });
}

// Create a new capture
export async function createCapture(
  frameId: string,
  originalImageBlob: Blob,
  processedImageBlob: Blob,
  detectionResults: DetectionResult[]
): Promise<Capture> {
  // Generate thumbnail BEFORE opening transaction to avoid timeout
  let thumbnailBlob: Blob | undefined;
  try {
    thumbnailBlob = await createThumbnail(processedImageBlob);
  } catch (error) {
    console.error('Failed to create thumbnail:', error);
    // Continue without thumbnail
  }
  
  const now = Date.now();
  const captureId = crypto.randomUUID();
  
  const capture: Capture = {
    captureId,
    frameId,
    captureTimestamp: now,
    originalImageBlob,
    processedImageBlob,
    thumbnailBlob,
    detectionResults,
    syncStatus: 'pending',
    syncVersion: 1
  };
  
  // Now open transaction and perform database operations
  const db = await getDB();
  const tx = db.transaction(['captures', 'syncQueue'], 'readwrite');
  
  // Add the capture
  await tx.objectStore('captures').add(capture);

  // Commit the transaction first
  await tx.done;

  // Add to sync queue using standardized helper
  await addCaptureToSyncQueue('create', captureId, frameId);

  // Notify reactive system about the new capture
  reactiveDatabase.notifyChange({
    type: 'capture',
    operation: 'create',
    id: captureId,
    frameId: frameId,
    changes: { captureId, frameId }
  });

  // Increment the capture count for the frame
  await incrementCaptureCount(frameId);

  console.log('[createCapture] Created capture and notified reactive system:', {
    captureId,
    frameId,
    timestamp: now
  });

  return capture;
}

// Get a capture by ID
export async function getCaptureById(captureId: string): Promise<Capture | undefined> {
  const db = await getDB();
  return db.get('captures', captureId);
}

// Get all captures for a frame
export async function getCapturesByFrameId(frameId: string): Promise<Capture[]> {
  const db = await getDB();
  const captures = await db.getAllFromIndex('captures', 'by-frame', frameId);
  
  // Sort by timestamp (newest first)
  return captures.sort((a, b) => b.captureTimestamp - a.captureTimestamp);
}

// Update a capture
export async function updateCapture(
  captureId: string,
  updates: Partial<Omit<Capture, 'captureId' | 'frameId' | 'captureTimestamp'>>
): Promise<Capture> {
  const db = await getDB();
  
  // Get the current capture
  const capture = await db.get('captures', captureId);
  if (!capture) {
    throw new Error(`Capture with ID ${captureId} not found`);
  }
  
  // Update the capture
  const updatedCapture: Capture = {
    ...capture,
    ...updates,
  };
  
  // If this is a sync manager update to mark as synced, don't override it
  if (updates.syncStatus === 'synced') {
    // This is a sync completion update - keep the synced status
    updatedCapture.syncStatus = 'synced';
  } else {
    // This is a user update - mark as pending and add to sync queue
    updatedCapture.syncStatus = 'pending';
    updatedCapture.syncVersion = capture.syncVersion + 1;
    
    // Add to sync queue using standardized helper
    await addCaptureToSyncQueue('update', captureId, capture.frameId);
  }
  
  await db.put('captures', updatedCapture);

  // Notify reactive system about the capture update
  reactiveDatabase.notifyChange({
    type: 'capture',
    operation: 'update',
    id: captureId,
    frameId: updatedCapture.frameId,
    changes: updates
  });

  return updatedCapture;
}

// Delete a capture
export async function deleteCapture(captureId: string): Promise<void> {
  const db = await getDB();
  const tx = db.transaction(['captures', 'syncQueue'], 'readwrite');
  
  // Get the capture to get the frameId
  const capture = await tx.objectStore('captures').get(captureId);
  if (!capture) {
    throw new Error(`Capture with ID ${captureId} not found`);
  }
  
  // Delete the capture
  await tx.objectStore('captures').delete(captureId);

  // Commit the transaction
  await tx.done;

  // Add to sync queue using standardized helper
  await addCaptureToSyncQueue('delete', captureId, capture.frameId);

  // Notify reactive system about the capture deletion
  reactiveDatabase.notifyChange({
    type: 'capture',
    operation: 'delete',
    id: captureId,
    frameId: capture.frameId,
    changes: { deleted: true }
  });

  // Update the frame capture count
  // Note: We don't decrement here because we want to keep track of total captures made
  // If needed, you could implement a separate function to accurately count captures
}

// Get only the metadata for captures (without the blobs, for efficiency)
export async function getCaptureMetadataByFrameId(frameId: string): Promise<Omit<Capture, 'originalImageBlob' | 'processedImageBlob'>[]> {
  const db = await getDB();
  const tx = db.transaction('captures', 'readonly');
  const index = tx.store.index('by-frame');
  
  const results: Omit<Capture, 'originalImageBlob' | 'processedImageBlob'>[] = [];
  let cursor = await index.openCursor(frameId);
  
  while (cursor) {
    const { originalImageBlob: _originalImageBlob, processedImageBlob: _processedImageBlob, ...metadata } = cursor.value;
    results.push(metadata);
    cursor = await cursor.continue();
  }
  
  // Sort by timestamp (newest first)
  return results.sort((a, b) => b.captureTimestamp - a.captureTimestamp);
}