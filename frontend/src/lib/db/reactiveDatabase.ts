'use client';

import { BehaviorSubject, Observable } from 'rxjs';
import { enhancedSyncStateManager } from '@/lib/sync/enhancedSyncStateManager';
import { updateCapture as originalUpdateCapture, update<PERSON>rame as originalUpdateFrame } from './captureOperations';
import { updateFrame as originalFrameUpdate } from './frameOperations';
import type { Capture, Frame } from './types';

export interface DatabaseChange {
  type: 'capture' | 'frame';
  id: string;
  frameId: string;
  operation: 'create' | 'update' | 'delete';
  changes: Record<string, any>;
  timestamp: number;
}

/**
 * Reactive Database Wrapper
 * 
 * Wraps database operations to emit reactive changes that trigger immediate UI updates.
 * Eliminates the need for polling by providing real-time change notifications.
 */
class ReactiveDatabase {
  private static instance: ReactiveDatabase;
  
  // Subject for database changes
  private changeSubject = new BehaviorSubject<DatabaseChange[]>([]);
  
  // Track recent changes (last 100)
  private recentChanges: DatabaseChange[] = [];

  private constructor() {
    // Clean up old changes periodically
    setInterval(() => {
      this.cleanupOldChanges();
    }, 60000); // Every minute
  }

  public static getInstance(): ReactiveDatabase {
    if (!ReactiveDatabase.instance) {
      ReactiveDatabase.instance = new ReactiveDatabase();
    }
    return ReactiveDatabase.instance;
  }

  /**
   * Reactive capture update with immediate state propagation
   */
  public async updateCapture(
    captureId: string, 
    updates: Partial<Capture>
  ): Promise<Capture> {
    console.log('[ReactiveDatabase] Updating capture:', { captureId, updates });
    
    // Perform the actual database update
    const updatedCapture = await originalUpdateCapture(captureId, updates);
    
    // Create change record
    const change: DatabaseChange = {
      type: 'capture',
      id: captureId,
      frameId: updatedCapture.frameId,
      operation: 'update',
      changes: updates,
      timestamp: Date.now()
    };
    
    // Add to recent changes
    this.recentChanges.push(change);
    this.recentChanges = this.recentChanges.slice(-100); // Keep last 100
    
    // Emit change
    this.changeSubject.next([...this.recentChanges]);
    
    // Update enhanced sync state manager if sync status changed
    if (updates.syncStatus) {
      enhancedSyncStateManager.updateItemSyncStatus(
        updatedCapture.frameId,
        captureId,
        'capture',
        updates.syncStatus
      );
    }
    
    console.log('[ReactiveDatabase] Capture updated and change emitted:', {
      captureId,
      frameId: updatedCapture.frameId,
      syncStatus: updates.syncStatus
    });
    
    return updatedCapture;
  }

  /**
   * Reactive frame update with immediate state propagation
   */
  public async updateFrame(
    frameId: string, 
    updates: Partial<Frame>
  ): Promise<Frame> {
    console.log('[ReactiveDatabase] Updating frame:', { frameId, updates });
    
    // Perform the actual database update
    const updatedFrame = await originalFrameUpdate(frameId, updates);
    
    // Create change record
    const change: DatabaseChange = {
      type: 'frame',
      id: frameId,
      frameId: frameId,
      operation: 'update',
      changes: updates,
      timestamp: Date.now()
    };
    
    // Add to recent changes
    this.recentChanges.push(change);
    this.recentChanges = this.recentChanges.slice(-100); // Keep last 100
    
    // Emit change
    this.changeSubject.next([...this.recentChanges]);
    
    // Update enhanced sync state manager if sync status changed
    if (updates.syncStatus) {
      enhancedSyncStateManager.updateItemSyncStatus(
        frameId,
        frameId,
        'frame',
        updates.syncStatus
      );
    }
    
    console.log('[ReactiveDatabase] Frame updated and change emitted:', {
      frameId,
      syncStatus: updates.syncStatus
    });
    
    return updatedFrame;
  }

  /**
   * Batch update multiple captures with single change emission
   */
  public async batchUpdateCaptures(
    updates: Array<{ captureId: string; updates: Partial<Capture> }>
  ): Promise<Capture[]> {
    console.log('[ReactiveDatabase] Batch updating captures:', updates.length);
    
    const results: Capture[] = [];
    const changes: DatabaseChange[] = [];
    
    // Perform all updates
    for (const { captureId, updates: captureUpdates } of updates) {
      const updatedCapture = await originalUpdateCapture(captureId, captureUpdates);
      results.push(updatedCapture);
      
      const change: DatabaseChange = {
        type: 'capture',
        id: captureId,
        frameId: updatedCapture.frameId,
        operation: 'update',
        changes: captureUpdates,
        timestamp: Date.now()
      };
      
      changes.push(change);
      
      // Update enhanced sync state manager if sync status changed
      if (captureUpdates.syncStatus) {
        enhancedSyncStateManager.updateItemSyncStatus(
          updatedCapture.frameId,
          captureId,
          'capture',
          captureUpdates.syncStatus
        );
      }
    }
    
    // Add to recent changes
    this.recentChanges.push(...changes);
    this.recentChanges = this.recentChanges.slice(-100); // Keep last 100
    
    // Emit all changes at once
    this.changeSubject.next([...this.recentChanges]);
    
    console.log('[ReactiveDatabase] Batch update completed:', {
      updatedCount: results.length,
      changesEmitted: changes.length
    });
    
    return results;
  }

  /**
   * Get observable stream of database changes
   */
  public getChanges(): Observable<DatabaseChange[]> {
    return this.changeSubject.asObservable();
  }

  /**
   * Get changes for a specific frame
   */
  public getFrameChanges(frameId: string): Observable<DatabaseChange[]> {
    return this.changeSubject.asObservable().pipe(
      map(changes => changes.filter(change => change.frameId === frameId))
    );
  }

  /**
   * Get recent changes snapshot
   */
  public getRecentChanges(): DatabaseChange[] {
    return [...this.recentChanges];
  }

  /**
   * Clear all tracked changes
   */
  public clearChanges(): void {
    this.recentChanges = [];
    this.changeSubject.next([]);
  }

  /**
   * Clean up old changes (older than 5 minutes)
   */
  private cleanupOldChanges(): void {
    const cutoff = Date.now() - 300000; // 5 minutes
    const oldLength = this.recentChanges.length;
    
    this.recentChanges = this.recentChanges.filter(
      change => change.timestamp > cutoff
    );
    
    if (this.recentChanges.length !== oldLength) {
      console.log('[ReactiveDatabase] Cleaned up old changes:', {
        removed: oldLength - this.recentChanges.length,
        remaining: this.recentChanges.length
      });
      this.changeSubject.next([...this.recentChanges]);
    }
  }
}

// Import map function
import { map } from 'rxjs/operators';

// Export singleton instance
export const reactiveDatabase = ReactiveDatabase.getInstance();

// Export class for testing
export { ReactiveDatabase };
